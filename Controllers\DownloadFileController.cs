
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;

namespace eCW.Controllers
{
    public class DownloadFileController : Controller
    {
        private readonly ILogger _Logger;
        private readonly IDataFactory _DataFactroy; // Keep your original naming

        public DownloadFileController(ILogger logger, IDataFactory dataFactory)
        {
            _Logger = logger;
            _DataFactroy = dataFactory;
        }

        public IActionResult Index()
        {
            return View();
        }

        [HttpPost]
        public IActionResult tableColumnsList(string data1)
        {
            if (string.IsNullOrEmpty(data1))
            {
                TempData["FailedMessage"] = "Invalid data received.";
                return RedirectToAction("Index");
            }

            try
            {
                var vm = JsonConvert.DeserializeObject<DownloadFileDropdownList>(data1);
                if (vm?.tableColumnsNames == null || vm.tableColumnsNames.Count() == 0)
                {
                    _Logger.Info("DownloadFileController ERROR: Please select at least one record.");
                    TempData["FailedMessage"] = "Please select at least one record.";
                    return RedirectToAction("Index");
                }

                var resultList = new List<DownloadFileDropdownList>();

                foreach (var columnName in vm.tableColumnsNames)
                {
                    // Load only first 50 records initially
                    var columnData = _DataFactroy.GetColumnNameByDataPaginated(columnName, 1, 50);

                    var columnItems = new List<SelectListItem>(columnData.Data.Count);

                    foreach (var item in columnData.Data)
                    {
                        var name = item.Name?.Trim();
                        if (!string.IsNullOrEmpty(name))
                        {
                            columnItems.Add(new SelectListItem
                            {
                                Text = name,
                                Value = name
                            });
                        }
                    }

                    resultList.Add(new DownloadFileDropdownList
                    {
                        ColumnsName = columnName,
                        ddltableColumns = columnItems
                    });
                }

                return View(resultList);
            }
            catch (Exception ex)
            {
                _Logger.Error("DownloadFileController ERROR in tableColumnsList: " + (ex.InnerException?.Message ?? ex.Message));
                TempData["FailedMessage"] = "An unexpected error occurred.";
                return RedirectToAction("Index");
            }
        }

        [HttpGet]
        public JsonResult GetColumnDataPaginated(string columnName, int page = 1, int pageSize = 50)
        {
            var columnData = _DataFactroy.GetColumnNameByDataPaginated(columnName, page, pageSize);
            return Json(new { 
                success = true, 
                data = columnItems,
                hasMore = columnData.HasMore
            });
        }
    }
}

// Add this model class for paginated results
public class PaginatedResult<T>
{
    public List<T> Data { get; set; } = new List<T>();
    public int TotalCount { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public bool HasMore => (CurrentPage * PageSize) < TotalCount;
}

// Interface for your data factory (add this to your existing interface)
public interface IDataFactory
{
    List<ItemDropdownListViewModel> GetColumnNameByData(string columnName);
    PaginatedResult<ItemDropdownListViewModel> GetColumnNameByDataPaginated(string columnName, int page, int pageSize);
}

