using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace eCW.DataAccess
{
    public class DownloadFileDataFactory : IDataFactory
    {
        private readonly string strConnection;
        private readonly ILogger _Logger;

        public DownloadFileDataFactory(string connectionString, ILogger logger)
        {
            strConnection = connectionString;
            _Logger = logger;
        }

        // Your existing method (keep for backward compatibility)
        public List<ItemDropdownListViewModel> GetColumnNameByData(string columnName)
        {
            var listColumns = new List<ItemDropdownListViewModel>();

            if (string.IsNullOrEmpty(strConnection) || string.IsNullOrEmpty(columnName))
                return listColumns;

            try
            {
                using (var con = new SqlConnection(strConnection))
                using (var cmd = new SqlCommand($"SELECT DISTINCT [{columnName}] FROM ExcelFileData", con))
                {
                    con.Open();

                    using (var reader = cmd.ExecuteReader())
                    {
                        int i = 1;
                        while (reader.Read())
                        {
                            string value = reader.IsDBNull(0) ? "NA" : reader.GetValue(0).ToString().Trim();
                            listColumns.Add(new ItemDropdownListViewModel
                            {
                                Id = i++,
                                Name = value
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _Logger.Error($"DownloadFileDF ERROR in GetColumnNameByData: {ex.InnerException?.Message ?? ex.Message}");
            }

            return listColumns;
        }

        // New paginated method
        public PaginatedResult<ItemDropdownListViewModel> GetColumnNameByDataPaginated(string columnName, int page, int pageSize)
        {
            var result = new PaginatedResult<ItemDropdownListViewModel>
            {
                CurrentPage = page,
                PageSize = pageSize
            };

            if (string.IsNullOrEmpty(strConnection) || string.IsNullOrEmpty(columnName))
                return result;

            try
            {
                using (var con = new SqlConnection(strConnection))
                {
                    con.Open();

                    // First, get the total count
                    using (var countCmd = new SqlCommand($"SELECT COUNT(DISTINCT [{columnName}]) FROM ExcelFileData WHERE [{columnName}] IS NOT NULL", con))
                    {
                        result.TotalCount = (int)countCmd.ExecuteScalar();
                    }

                    // Then get the paginated data
                    var offset = (page - 1) * pageSize;
                    var sql = $@"SELECT DISTINCT [{columnName}] 
                                FROM ExcelFileData 
                                WHERE [{columnName}] IS NOT NULL
                                ORDER BY [{columnName}]
                                OFFSET {offset} ROWS 
                                FETCH NEXT {pageSize} ROWS ONLY";

                    using (var cmd = new SqlCommand(sql, con))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            int i = offset + 1;
                            while (reader.Read())
                            {
                                string value = reader.IsDBNull(0) ? "NA" : reader.GetValue(0).ToString().Trim();
                                result.Data.Add(new ItemDropdownListViewModel
                                {
                                    Id = i++,
                                    Name = value
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _Logger.Error($"DownloadFileDF ERROR in GetColumnNameByDataPaginated: {ex.InnerException?.Message ?? ex.Message}");
            }

            return result;
        }
    }
}

// Model classes (add these if they don't exist)
public class ItemDropdownListViewModel
{
    public int Id { get; set; }
    public string Name { get; set; }
}

public class DownloadFileDropdownList
{
    public string ColumnsName { get; set; }
    public List<string> tableColumnsNames { get; set; }
    public List<SelectListItem> ddltableColumns { get; set; }
}

