/**
 * Dropdown Pagination Plugin
 * Handles infinite scrolling for multiselect dropdowns
 */
(function($) {
    'use strict';

    var DropdownPagination = function(element, options) {
        this.element = $(element);
        this.options = $.extend({}, DropdownPagination.DEFAULTS, options);
        this.currentPage = 1;
        this.hasMore = true;
        this.loading = false;
        this.columnName = this.element.data('column');
        
        this.init();
    };

    DropdownPagination.DEFAULTS = {
        pageSize: 50,
        threshold: 10, // pixels from bottom to trigger load
        loadingText: 'Loading more...',
        ajaxUrl: '/DownloadFile/GetColumnDataPaginated',
        debug: false
    };

    DropdownPagination.prototype = {
        init: function() {
            var self = this;
            
            // Initialize multiselect first
            this.initMultiselect();
            
            // Setup scroll listener after multiselect is ready
            setTimeout(function() {
                self.setupScrollListener();
            }, 200);
        },

        initMultiselect: function() {
            var self = this;
            
            this.element.multiselect({
                includeSelectAllOption: true,
                enableFiltering: true,
                filterPlaceholder: 'Search...',
                nonSelectedText: 'Select columns',
                selectAllText: 'Select All',
                deselectAllText: 'Deselect All',
                buttonWidth: '100%',
                maxHeight: 300,
                onDropdownShow: function() {
                    // Re-setup scroll listener when dropdown opens
                    setTimeout(function() {
                        self.setupScrollListener();
                    }, 50);
                }
            });
        },

        setupScrollListener: function() {
            var self = this;
            
            // Find the scrollable container - try different selectors for different multiselect versions
            var $container = this.findScrollContainer();
            
            if ($container.length === 0) {
                if (this.options.debug) {
                    console.warn('Could not find scroll container for', this.columnName);
                }
                return;
            }

            // Remove existing listeners
            $container.off('scroll.pagination');
            
            // Add scroll listener
            $container.on('scroll.pagination', function() {
                if (self.loading || !self.hasMore) return;
                
                var scrollTop = this.scrollTop;
                var scrollHeight = this.scrollHeight;
                var clientHeight = this.clientHeight;
                
                // Check if near bottom
                if (scrollTop + clientHeight >= scrollHeight - self.options.threshold) {
                    self.loadMoreData();
                }
            });

            if (this.options.debug) {
                console.log('Scroll listener setup for', this.columnName, $container);
            }
        },

        findScrollContainer: function() {
            var $container;
            
            // Try different selectors based on multiselect plugin version
            var selectors = [
                '.btn-group .multiselect-container',
                '.multiselect-container .dropdown-menu',
                '.multiselect-container ul',
                '.btn-group .dropdown-menu'
            ];
            
            for (var i = 0; i < selectors.length; i++) {
                $container = this.element.siblings(selectors[i]);
                if ($container.length > 0) {
                    break;
                }
            }
            
            return $container;
        },

        loadMoreData: function() {
            var self = this;
            
            if (this.loading || !this.hasMore) return;
            
            this.loading = true;
            this.currentPage++;
            
            // Show loading indicator
            this.showLoadingIndicator();
            
            $.ajax({
                url: this.options.ajaxUrl,
                type: 'GET',
                data: {
                    columnName: this.columnName,
                    page: this.currentPage,
                    pageSize: this.options.pageSize
                },
                success: function(response) {
                    self.handleLoadSuccess(response);
                },
                error: function(xhr, status, error) {
                    self.handleLoadError(error);
                },
                complete: function() {
                    self.hideLoadingIndicator();
                    self.loading = false;
                }
            });
        },

        handleLoadSuccess: function(response) {
            if (response.success && response.data && response.data.length > 0) {
                // Add new options
                var self = this;
                response.data.forEach(function(item) {
                    var optionExists = self.element.find('option[value="' + item.Value + '"]').length > 0;
                    if (!optionExists) {
                        self.element.append(new Option(item.Text, item.Value));
                    }
                });
                
                // Update state
                this.hasMore = response.hasMore;
                
                // Rebuild multiselect
                this.element.multiselect('rebuild');
                
                // Re-setup scroll listener
                var self = this;
                setTimeout(function() {
                    self.setupScrollListener();
                }, 100);
                
                if (this.options.debug) {
                    console.log('Loaded page', this.currentPage, 'for', this.columnName, '- hasMore:', this.hasMore);
                }
            } else {
                this.hasMore = false;
            }
        },

        handleLoadError: function(error) {
            console.error('Error loading more data for', this.columnName, ':', error);
            this.hasMore = false;
        },

        showLoadingIndicator: function() {
            var $container = this.findScrollContainer();
            if ($container.length > 0) {
                var $indicator = $('<li class="pagination-loading"><a href="#" style="text-align: center; color: #999; cursor: default;">' + 
                                 this.options.loadingText + '</a></li>');
                
                // Try to find ul element within container
                var $ul = $container.find('ul');
                if ($ul.length > 0) {
                    $ul.append($indicator);
                } else {
                    $container.append($indicator);
                }
            }
        },

        hideLoadingIndicator: function() {
            $('.pagination-loading').remove();
        }
    };

    // jQuery plugin definition
    $.fn.dropdownPagination = function(options) {
        return this.each(function() {
            var $this = $(this);
            var data = $this.data('dropdown-pagination');
            
            if (!data) {
                $this.data('dropdown-pagination', new DropdownPagination(this, options));
            }
        });
    };

    // Auto-initialize on elements with data-pagination attribute
    $(document).ready(function() {
        $('[data-pagination="true"]').dropdownPagination();
    });

})(jQuery);
