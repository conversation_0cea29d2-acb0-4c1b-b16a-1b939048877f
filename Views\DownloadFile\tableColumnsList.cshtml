
@model List<eCW.ModelView.DownloadFileViewModel.DownloadFileDropdownList>;
@{
    ViewBag.Title = "Table Columns List";
    Layout = "~/Views/Shared/_Layout.cshtml";
    string ddlTableDropdownList = string.Empty;
}

<style type="text/css">
    .scrollable-div {
        height: 350px;
        overflow-y: scroll;
    }
    
    .loading-indicator {
        text-align: center;
        padding: 10px;
        display: none;
    }
    
    .multiselect-container {
        max-height: 300px;
        overflow-y: auto;
    }
</style>

<div class="overview">
    <div class="title">
        <i class="fa-solid fa-list"></i>
        <span class="text">Table Columns List</span>
    </div>
    
    <partial name="_Alert" />

    <form asp-action="tableColumnsList" method="post">
        <div class="row">
            <table class="BlueGreen" style="width:100%">
                <thead>
                    <tr>
                        <th>Column Name</th>
                        <th>Select Value</th>
                    </tr>
                </thead>
                <tbody>
                    @for (int i = 0; i < Model.Count; i++)
                    {
                        <tr>
                            <td style="width:30%">
                                @Html.TextBoxFor(m => m[i].ColumnsName, new { @class = "form-control", autocomplete = "off", onpaste = "return false", onkeypress = "return false;", onKeyDown = "javascript: return false;" })
                            </td>
                            <td style="width:70%">
                                @{
                                    ddlTableDropdownList = "ddlTableDropdownList" + Model[i].ColumnsName;
                                }
                                @Html.ListBoxFor(m => m[i].tableColumnsNames, 
                                    new MultiSelectList(Model[i].ddltableColumns, "Value", "Text"), 
                                    new { @class = "form-control tableColumnsNamesdiv", 
                                          multiple = "multiple", 
                                          @id = ddlTableDropdownList, 
                                          data_column = Model[i].ColumnsName,
                                          data_pagination = "true" })
                            </td>
                        </tr>
                    }
                </tbody>
            </table>

            <div class="row">
                <div class="col-2">
                    <div class="form-group">
                        <button type="submit" title="submit" class="btn btn-primary">Submit</button>
                    </div>
                </div>
                <div class="col-2">
                    <div class="form-group">
                        <a asp-controller="DownloadFile" title="Cancel" asp-action="Index" class="btn btn-warning">Cancel</a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Include the pagination plugin -->
<script src="~/Scripts/dropdown-pagination.js"></script>

<script type="text/javascript">
    $(function () {
        // Initialize dropdown pagination for all elements with data-pagination="true"
        $('.tableColumnsNamesdiv[data-pagination="true"]').dropdownPagination({
            pageSize: 50,
            threshold: 10,
            ajaxUrl: '@Url.Action("GetColumnDataPaginated", "DownloadFile")',
            debug: false // Set to true for debugging
        });
    });
</script>

